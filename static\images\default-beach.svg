<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e1f5fe;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b3e5fc;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="150" fill="url(#bg)" rx="8"/>
  <ellipse cx="100" cy="90" rx="60" ry="15" fill="#00bcd4" opacity="0.6"/>
  <path d="M40 90 Q100 80 160 90 Q100 85 40 90" fill="#4fc3f7" opacity="0.8"/>
  <circle cx="60" cy="40" r="15" fill="#ffeb3b" opacity="0.8"/>
  <path d="M55 35 L65 35 M55 40 L65 40 M55 45 L65 45" stroke="#ffc107" stroke-width="1"/>
  <text x="100" y="120" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#00bcd4">海滩沙滩</text>
  <text x="100" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">暂无图片</text>
</svg>
