<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#fff3e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ffe0b2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="150" fill="url(#bg)" rx="8"/>
  <circle cx="100" cy="60" r="20" fill="#ff9800" opacity="0.7"/>
  <path d="M90 50 L90 70 M95 50 L95 70 M100 50 L100 70 M105 50 L105 70 M110 50 L110 70" stroke="#f57c00" stroke-width="2"/>
  <rect x="85" y="65" width="30" height="8" fill="#f57c00" rx="2"/>
  <text x="100" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#f57c00">餐厅美食</text>
  <text x="100" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">暂无图片</text>
</svg>
