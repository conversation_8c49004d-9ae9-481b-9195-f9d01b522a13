<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e8f5e8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#c8e6c9;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="150" fill="url(#bg)" rx="8"/>
  <path d="M70 70 Q100 40 130 70 Q100 50 70 70" fill="#4caf50" opacity="0.7"/>
  <rect x="95" y="60" width="10" height="15" fill="#8bc34a"/>
  <circle cx="85" cy="65" r="3" fill="#ffeb3b"/>
  <circle cx="115" cy="68" r="2" fill="#ff5722"/>
  <text x="100" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#4caf50">风景名胜</text>
  <text x="100" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">暂无图片</text>
</svg>
