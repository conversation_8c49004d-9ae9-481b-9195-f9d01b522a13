<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e3f2fd;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#bbdefb;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="150" fill="url(#bg)" rx="8"/>
  <circle cx="100" cy="60" r="20" fill="#2196f3" opacity="0.7"/>
  <path d="M100 40 L110 60 L100 80 L90 60 Z" fill="#1976d2"/>
  <text x="100" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#1976d2">景点图片</text>
  <text x="100" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">暂无图片</text>
</svg>
