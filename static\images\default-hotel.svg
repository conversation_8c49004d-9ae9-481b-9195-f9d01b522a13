<svg width="200" height="150" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f3e5f5;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e1bee7;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="200" height="150" fill="url(#bg)" rx="8"/>
  <rect x="80" y="40" width="40" height="30" fill="#9c27b0" opacity="0.7" rx="2"/>
  <rect x="85" y="45" width="8" height="8" fill="#fff" rx="1"/>
  <rect x="95" y="45" width="8" height="8" fill="#fff" rx="1"/>
  <rect x="107" y="45" width="8" height="8" fill="#fff" rx="1"/>
  <rect x="85" y="57" width="8" height="8" fill="#fff" rx="1"/>
  <rect x="95" y="57" width="8" height="8" fill="#fff" rx="1"/>
  <rect x="107" y="57" width="8" height="8" fill="#fff" rx="1"/>
  <text x="100" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#9c27b0">酒店住宿</text>
  <text x="100" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="#666">暂无图片</text>
</svg>
