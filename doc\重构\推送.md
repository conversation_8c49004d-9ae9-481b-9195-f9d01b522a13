# Agent实时推送与前端交互改造方案

本文档旨在阐述一套完整的前后端实时通信方案，以提升用户体验。

## 二、实时推送与状态追踪方案

为了从根本上解决问题并优化体验，我们设计一套基于SSE（Server-Sent Events）和Redis的实时通知方案。

### 1. 后端：`NotificationService`核心设计

我们在后端引入一个`NotificationService`，它被嵌在LangGraph的每个关键节点中，负责：

*   **生成唯一ID**：为每个执行的子任务（如“核心意图分析”）生成一个唯一的`step_id`。
*   **推送开始事件 (`step_start`)**：当一个子任务开始时，立即通过SSE向前端推送一个`step_start`事件。该事件包含：
    *   `step_id`：该子任务的唯一标识。
    *   `step_name`：任务的名称，如`core_intent_analysis`。
    *   `message`：用于在UI上展示给用户的文本，如“正在分析您的旅行意图...”。
*   **推送结束事件 (`step_end`)**：当子任务结束时，通过SSE推送`step_end`事件，包含：
    *   `step_id`：与开始事件对应。
    *   `status`：`success`或`error`。
    *   `result`：成功时，携带该步骤的结构化产出，如意图分析的结果。
    *   `message`：结束时的提示信息。
*   **同步Redis状态**：所有`step_start`和`step_end`事件的关键信息，都会以`step_id`为键，实时存入Redis。这使得前端即使刷新页面，也能通过查询后端来恢复当前任务的执行状态。

### 2. 事件流示例

1.  前端点击“立即规划”。
2.  后端`core_intent_analyzer_node`节点执行。
3.  `NotificationService`立刻推送SSE事件: `{ "event": "step_start", "data": { "step_id": "...", "message": "正在分析您的旅行意图..." } }`
4.  LLM完成分析。
5.  `NotificationService`推送SSE事件: `{ "event": "step_end", "data": { "step_id": "...", "status": "success", "result": { "destinations": ["莆田"], ... } } }`
6.  流程进入下一个节点，重复3-5步。

---

## 三、前端改造指南

前端的核心任务是**实现请求锁**，并**消费SSE事件来实时更新UI**。主要修改将集中在`static/js/app-refactored.js`文件中。

### 1. 核心原则：请求锁与状态管理

*   在 `app` 的 data/state 中增加一个状态标记，如 `isPlanning: false`。
*   **加锁**：当用户点击“立即规划”时，首先检查 `this.isPlanning`。如果为 `true`，则直接`return`，不执行任何操作。如果为 `false`，则立刻设置为 `true`，并禁用“立即规划”按钮。
*   **解锁**：只有在整个规划流程完全结束（接收到最终的`completed`或`error`事件）后，才将`isPlanning`设置回`false`，并恢复按钮状态。

### 2. 具体文件修改建议 (`static/js/app-refactored.js`)

#### a. 启动规划函数（例如 `startPlanning()` 或类似的事件处理函数）
```javascript
// 修改前的伪代码
startPlanning() {
  // 直接发送请求
  fetch('/plan', ...);
}

// 修改后的伪代码
startPlanning() {
  // 1. 加锁
  if (this.isPlanning) {
    console.log("已有规划任务在进行中，请勿重复点击。");
    return;
  }
  this.isPlanning = true;
  document.getElementById('plan-button').disabled = true; // 禁用按钮

  // 2. 清理旧的UI状态
  this.clearPreviousResults(); 

  // 3. 发送请求并建立SSE连接
  this.connectToSSE('/plan', ...);
}
```

#### b. SSE事件处理函数（例如 `handleSSEEvent(data)`)
```javascript
// 修改前的伪代码
handleSSEEvent(data) {
  // 可能只是笼统地处理最终结果
}

// 修改后的伪代码
handleSSEEvent(data) {
  const event = JSON.parse(data); // 解析从SSE收到的JSON字符串
  
  switch(event.type) { // 根据我们设计的事件类型来处理
    case 'step_start':
      // 一个新的子任务开始了
      console.log(`步骤 ${event.data.step_name} 开始: ${event.data.message}`);
      this.showStepInProgress(event.data.step_id, event.data.message);
      break;
      
    case 'step_end':
      // 一个子任务结束了
      console.log(`步骤 ${event.data.step_name} 结束，状态: ${event.data.status}`);
      if (event.data.status === 'success') {
        this.showStepSuccess(event.data.step_id, event.data.result);
        
        // 特殊处理：如果是最终完成事件
        if (event.data.step_name === 'completed') {
            this.isPlanning = false; // 解锁
            document.getElementById('plan-button').disabled = false;
        }
      } else {
        this.showStepError(event.data.step_id, event.data.message);
        this.isPlanning = false; // 出现错误，也要解锁
        document.getElementById('plan-button').disabled = false;
      }
      break;
    
    // ...处理其他可能的事件类型
  }
}
```

#### c. 新增的UI更新函数
需要创建一系列新的函数来根据SSE事件更新界面：
*   `showStepInProgress(stepId, message)`: 在界面上找到或创建一个代表该步骤的UI元素，并显示加载状态（转圈圈）和提示信息。
*   `showStepSuccess(stepId, result)`: 找到对应的UI元素，将其状态更新为“成功”（如显示绿色对勾），并使用`result`中的数据显示分析结果。例如，在“核心意图分析”成功后，就用其`result`来填充“莆田 | 2天 | ... | 自驾”这个卡片。
*   `showStepError(stepId, errorMessage)`: 将UI元素状态更新为“失败”，并显示错误信息。

通过以上改造，前端将能够精确地反映后端每一步的状态，彻底杜绝重复请求的问题，为用户提供清晰、流畅的交互体验。

---

## 四、UI组件动态化方案 (进阶)

为了进一步解耦前后端，我们可以将UI上各个分析步骤的标题（如“解析用户需求和画像”）也由后端通过SSE事件来动态下发。

### 1. 设计思想

将UI的显示文本从静态的Hardcode（硬编码）转变为由后端下发的动态数据。

*   **优点**: 当后端业务流程发生变化（如步骤名称调整、增删步骤）时，前端无需修改代码和重新部署，UI会自动根据后端推送的数据进行渲染，极大提升了系统的灵活性和可维护性。

### 2. 后端改造：丰富 `step_start` 事件

我们需要在 `step_start` 事件的数据体中，增加一个`title`字段。

*   **调整后的 `step_start` 事件载荷**:
    ```json
    {
      "event": "step_start",
      "data": {
        "step_id": "...",
        "step_name": "core_intent_analysis",
        "title": "解析用户需求和画像",
        "message": "正在分析您的旅行意图...",
        "timestamp": "..."
      }
    }
    ```
*   **`NotificationService`改造**: `notify_step_start` 方法的签名需要从 `(self, step_name, message)` 调整为 `(self, step_name, title, message)`。
*   **`nodes.py`调用改造**: 在每个节点函数的开头，调用通知服务时需要传入对应的标题文本。
    ```python
    # 示例：在 core_intent_analyzer_node 中
    await notification_service.notify_step_start(
        step_name="core_intent_analysis",
        title="解析用户需求和画像",
        message="正在分析您的旅行意图..."
    )
    ```

### 3. 前端改造：动态渲染标题

前端需要放弃硬编码的标题，转而消费 `step_start` 事件中的 `title` 字段。

#### a. HTML 结构调整

为每个步骤卡片（Step Card）的标题区域设置一个动态ID。

*   **改造前 (示意)**:
    ```html
    <div class="step-card">
        <h3>解析用户需求和画像</h3>
        <p class="status">进行中...</p>
    </div>
    ```
*   **改造后 (示意)**: 当 `step_start` 事件到达时，JS动态创建如下结构。
    ```html
    <div class="step-card" id="step-card-{{step_id}}">
        <h3 id="title-{{step_id}}"></h3>  <!-- 标题容器 -->
        <p id="message-{{step_id}}"></p> <!-- 状态消息容器 -->
    </div>
    ```

#### b. JavaScript (`app-refactored.js`) 逻辑调整

`showStepInProgress` 函数需要接收并使用 `title`。

```javascript
// 修改前的伪代码
showStepInProgress(stepId, message) {
    // 找到一个预设的UI元素，更新它的消息
}

// 修改后的伪代码
showStepInProgress(stepId, title, message) {
    // 1. 动态创建一个新的步骤卡片UI元素
    const cardHtml = `<div class="step-card" id="step-card-${stepId}">
                        <h3 id="title-${stepId}"></h3>
                        <p id="message-${stepId}"></p>
                      </div>`;
    document.getElementById('steps-container').innerHTML += cardHtml;

    // 2. 使用从SSE事件中获取的数据填充内容
    document.getElementById(`title-${stepId}`).textContent = title;
    document.getElementById(`message-${stepId}`).textContent = message;
    
    // 3. 在这里可以添加“转圈圈”的加载动画
    // ...
}

// handleSSEEvent 函数也需要相应调整
handleSSEEvent(data) {
  // ...
  case 'step_start':
    this.showStepInProgress(
        event.data.step_id, 
        event.data.title, // 传递新的title字段
        event.data.message
    );
    break;
  // ...
}
```

通过以上改造，我们就实现了一个更加健壮和灵活的、由后端驱动的前端UI更新机制。
