"""
统一配置管理模块

基于 pydantic-settings 实现的配置系统，支持从环境变量和配置文件加载设置。
按角色区分LLM配置，支持 OpenAI 兼容的接口标准。
"""
from typing import Optional, Any, Dict, Type
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict, PydanticBaseSettingsSource
import yaml
import os
from pathlib import Path


class YamlConfigSettingsSource(PydanticBaseSettingsSource):
    """
    一个pydantic-settings的设置源，用于从YAML文件中加载值。
    确保使用UTF-8编码读取文件，解决Windows下的编码问题。
    """
    
    def get_field_value(self, field_info, field_name: str):
        # 这个方法在新版本中需要实现，但我们主要使用prepare
        return None
    
    def prepare_field_value(self, field_name: str, field, value, value_is_complex: bool):
        # 这个方法在新版本中需要实现，但我们主要使用__call__
        return value
    
    def __call__(self) -> Dict[str, Any]:
        # 确定YAML文件的路径
        config_path = Path(__file__).parent.parent.parent / "config" / "default.yaml"
        if not config_path.exists():
            return {}
        
        try:
            # 使用UTF-8编码读取和解析YAML文件
            with open(config_path, "r", encoding="utf-8") as f:
                data = yaml.safe_load(f)
                return data if data is not None else {}
        except Exception as e:
            print(f"Warning: Failed to load YAML config: {e}")
            return {}
    
    def __repr__(self) -> str:
        return 'YamlConfigSettingsSource(yaml_file="config/default.yaml")'


class MySQLConfig(BaseModel):
    """MySQL数据库连接配置"""
    host: str = Field("***********", description="数据库主机地址")
    user: str = Field("root", description="数据库用户名")
    password: str = Field("Fsit#2024", description="数据库密码")
    database: str = Field("dh_tripplanner", description="数据库名")
    port: int = Field(3306, description="数据库端口")


class MinioConfig(BaseModel):
    """MinIO对象存储配置"""
    endpoint: str = Field("***********:9000", description="MinIO服务器地址")
    access_key: str = Field("admin", description="MinIO访问密钥")
    secret_key: str = Field("Fsti#2024", description="MinIO秘密密钥")
    bucket: str = Field("aivlog", description="MinIO存储桶名称")
    secure: bool = Field(False, description="是否使用HTTPS")


class MongoDBConfig(BaseModel):
    """MongoDB数据库连接配置"""
    host: str = Field("***********", description="MongoDB主机地址")
    port: int = Field(27017, description="MongoDB端口")
    username: str = Field("admin", description="MongoDB用户名")
    password: str = Field("m25uids*g", description="MongoDB密码")
    database: str = Field("dh_platform_data", description="MongoDB数据库名")


class RedisConfig(BaseModel):
    """Redis连接配置"""
    host: str = Field("***********", description="Redis主机地址")
    port: int = Field(5182, description="Redis端口")
    password: str = Field("kLKe3NFM4RZMgXhA", description="Redis密码")
    db: int = Field(0, description="Redis数据库编号")
    expiry_time: int = Field(86400, description="默认过期时间（秒）")


class AppConfig(BaseModel):
    """应用配置"""
    name: str = Field("AutoPilot AI", description="应用名称")
    version: str = Field("0.1.0", description="应用版本")
    environment: str = Field("development", description="运行环境")


class ApiConfig(BaseModel):
    """API配置"""
    host: str = Field("0.0.0.0", description="API主机地址")
    port: int = Field(8000, description="API端口")
    reload: bool = Field(True, description="是否热加载")
    docs_url: str = Field("/docs", description="API文档URL")
    redoc_url: str = Field("/redoc", description="ReDoc文档URL")


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = Field("INFO", description="日志级别")
    format: str = Field("json", description="日志格式")


class MemoryConfig(BaseModel):
    """记忆配置"""
    short_term_ttl: int = Field(3600, description="L1短期记忆TTL (秒)")
    long_term_batch_size: int = Field(50, description="L2长期记忆批量大小")


class PromptsConfig(BaseModel):
    """Prompt模板配置"""
    template_dir: str = Field("src/prompts/templates", description="Prompt模板目录")
    auto_reload: bool = Field(True, description="是否自动重载模板")


class LLMConfig(BaseModel):
    """
    遵循OpenAI接口标准的LLM配置
    
    支持任何兼容OpenAI API的模型服务，包括本地部署的Ollama、vLLM等。
    """
    model: str = Field(..., description="模型名称")
    api_key: str = Field(..., description="API密钥")
    base_url: Optional[str] = Field(None, description="API基础URL")
    api_version: Optional[str] = Field(None, description="API版本")


class TeleSignConfig(BaseModel):
    """Tele签名相关配置"""
    region: str = Field("QG", description="Tele签名区域")
    expiration_in_seconds: int = Field(3600, description="签名过期时间（秒）")
    signed_headers: str = Field("x-app-id", description="签名头")
    public_network_sign_header: str = Field("teleai-cloud-auth-v1", description="公网鉴权头部")
    ems_sign_header: str = Field("eop-auth-v1", description="内网鉴权头部")
    x_app_id: str = Field("eac3f649473a473cab61f45373542f61", description="Tele账号ID")
    x_app_key: str = Field("68d4fdc30be34a42a446e35e1f82e349", description="Tele账号KEY")
    api_url: str = Field("/aipaas/voice/v1/asr/fy", description="ASR服务WebSocket地址")
    ws_url: str = Field("wss://openapi.teleagi.cn:443/aipaas/voice/v1/asr/fy", description="ASR WebSocket地址")
    method: str = Field("GET", description="请求方法")


class Settings(BaseSettings):
    """
    应用全局配置类
    
    通过环境变量加载敏感信息，从 config/*.yaml 加载非敏感配置。
    支持按角色区分的LLM配置。
    """
    # 数据库配置
    mysql: MySQLConfig = Field(
        default_factory=MySQLConfig,
        description="MySQL数据库配置"
    )

    minio: MinioConfig = Field(
        default_factory=MinioConfig,
        description="MinIO配置"
    )

    mongodb: MongoDBConfig = Field(
        default_factory=MongoDBConfig,
        description="MongoDB数据库配置"
    )

    redis: RedisConfig = Field(
        default_factory=RedisConfig,
        description="Redis配置"
    )

    # 应用配置
    app: AppConfig = Field(
        default_factory=AppConfig,
        description="应用配置"
    )

    # API 配置
    api: ApiConfig = Field(
        default_factory=ApiConfig,
        description="API配置"
    )

    # 日志配置
    logging: LoggingConfig = Field(
        default_factory=LoggingConfig,
        description="日志配置"
    )

    # 记忆配置
    memory: MemoryConfig = Field(
        default_factory=MemoryConfig,
        description="记忆配置"
    )

    # Prompt 配置
    prompts: PromptsConfig = Field(
        default_factory=PromptsConfig,
        description="Prompt模板配置"
    )

    # LLM 配置 - 这些是必需的字段
    reasoning_llm: LLMConfig
    basic_llm: LLMConfig
    map_llm: LLMConfig

    # ASR 配置 Tele签名配置
    tele_sign: TeleSignConfig = Field(default_factory=TeleSignConfig, description="Tele签名相关配置")

    @classmethod
    def settings_customise_sources(
        cls,
        settings_cls: Type[BaseSettings],
        init_settings: PydanticBaseSettingsSource,
        env_settings: PydanticBaseSettingsSource,
        dotenv_settings: PydanticBaseSettingsSource,
        file_secret_settings: PydanticBaseSettingsSource,
    ) -> tuple[PydanticBaseSettingsSource, ...]:
        """
        自定义配置加载源的顺序。
        
        优先级从高到低：
        1. 初始化参数
        2. 环境变量
        3. .env文件
        4. YAML配置文件（我们的自定义源）
        5. 文件密钥
        
        这样环境变量可以覆盖YAML文件中的配置。
        """
        return (
            init_settings,
            env_settings,
            dotenv_settings,
            YamlConfigSettingsSource(settings_cls),
            file_secret_settings,
        )

    # 配置模型设置，移除错误的yaml_file参数
    model_config = SettingsConfigDict(
        env_file='.env',
        env_file_encoding='utf-8',
        extra='ignore',
        case_sensitive=False
    )

    # 其他基础设施配置
    redis_url: str = Field(
        default="redis://localhost:6379",
        description="Redis连接URL"
    )

    comfyui_url: str = Field(
        default="http://************:8193",
        description="ComfyUI服务URL"
    )

    money_printer_api: str = Field(..., description="AI视频生成API地址")
    multimodal_api_url: str = Field(..., description="多模态API地址")

    def get_llm_config_by_role(self, role: str) -> LLMConfig:
        """
        根据角色获取对应的LLM配置

        Args:
            role: LLM角色，支持 'reasoning', 'basic', 'map'

        Returns:
            对应角色的LLMConfig实例

        Raises:
            ValueError: 当角色不存在时
        """
        if role == "reasoning":
            return self.reasoning_llm
        elif role == "basic":
            return self.basic_llm
        elif role == "map":
            return self.map_llm
        else:
            raise ValueError(f"Unknown LLM role: {role}")


# 延迟实例化的全局单例函数
_settings_instance: Optional[Settings] = None


def get_settings() -> Settings:
    """
    获取全局Settings实例（延迟实例化）
    """
    global _settings_instance
    if _settings_instance is None:
        _settings_instance = Settings()
    return _settings_instance


# 为了向后兼容，提供一个settings属性
class SettingsProxy:
    """Settings代理类，提供延迟加载的settings访问"""
    def __getattr__(self, name):
        return getattr(get_settings(), name)


settings = SettingsProxy()


# 使用示例:
# from src.core.config import settings
# planner_llm_config = settings.get_llm_config_by_role("reasoning")
# agent = PlannerAgent(llm_config=planner_llm_config.model_dump())